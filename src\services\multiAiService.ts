import { AIProjectRequest, AIProjectResponse, AIProjectService } from './aiService'
import { CohereProjectService } from './cohereService'
import { logger } from '../utils/logger'

export type AIProvider = 'gemini' | 'cohere' | 'auto'

export interface MultiAIServiceConfig {
  preferredProvider: AIProvider
  fallbackEnabled: boolean
  maxRetries: number
}

export class MultiAIService {
  private static config: MultiAIServiceConfig = {
    preferredProvider: (import.meta.env.VITE_PREFERRED_AI_PROVIDER as AIProvider) || 'auto',
    fallbackEnabled: import.meta.env.VITE_AI_FALLBACK_ENABLED !== 'false',
    maxRetries: Number(import.meta.env.VITE_AI_MAX_RETRIES) || 3,
  }

  /**
   * Generate a project using the best available AI provider
   */
  static async generateProject(request: AIProjectRequest): Promise<AIProjectResponse> {
    logger.info('Starting multi-AI project generation', {
      preferredProvider: this.config.preferredProvider,
      fallbackEnabled: this.config.fallbackEnabled
    }, 'multi-ai-service')

    const providers = this.getProviderOrder()
    
    for (const provider of providers) {
      try {
        console.log(`🤖 Trying ${provider} AI provider...`)
        const response = await this.callProvider(provider, request)
        
        logger.info('Multi-AI project generation successful', {
          provider,
          projectName: response.projectName
        }, 'multi-ai-service')
        
        return response
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        console.warn(`⚠️ ${provider} provider failed:`, errorMessage)
        
        logger.warn(`AI provider ${provider} failed`, {
          error: errorMessage,
          provider
        }, 'multi-ai-service')

        // If this is the last provider and fallback is disabled, throw the error
        if (provider === providers[providers.length - 1] && !this.config.fallbackEnabled) {
          throw error
        }
      }
    }

    // If all providers failed and fallback is enabled, use the basic fallback
    if (this.config.fallbackEnabled) {
      console.log('🔄 All AI providers failed, using basic fallback...')
      logger.info('All AI providers failed, using fallback', undefined, 'multi-ai-service')
      return AIProjectService.generateProject(request) // This will use the fallback method
    }

    throw new Error('All AI providers failed and fallback is disabled')
  }

  /**
   * Test connection to all available AI providers
   */
  static async testAllConnections(): Promise<{
    gemini: { success: boolean; message: string }
    cohere: { success: boolean; message: string }
    summary: { workingProviders: string[]; recommendedProvider: string | null }
  }> {
    console.log('🧪 Testing all AI provider connections...')

    const geminiTest = await AIProjectService.testConnection()
    const cohereTest = await CohereProjectService.testConnection()

    const workingProviders: string[] = []
    if (geminiTest.success) workingProviders.push('gemini')
    if (cohereTest.success) workingProviders.push('cohere')

    const recommendedProvider = this.getRecommendedProvider(workingProviders)

    return {
      gemini: geminiTest,
      cohere: cohereTest,
      summary: {
        workingProviders,
        recommendedProvider
      }
    }
  }

  /**
   * Get information about all AI services
   */
  static getAllServiceInfo(): {
    gemini: { provider: string; model: string; available: boolean }
    cohere: { provider: string; model: string; available: boolean }
    config: MultiAIServiceConfig
  } {
    return {
      gemini: AIProjectService.getAIServiceInfo(),
      cohere: CohereProjectService.getCohereServiceInfo(),
      config: this.config
    }
  }

  /**
   * Update the preferred AI provider
   */
  static setPreferredProvider(provider: AIProvider): void {
    this.config.preferredProvider = provider
    logger.info('Preferred AI provider updated', { provider }, 'multi-ai-service')
  }

  /**
   * Get the current preferred provider
   */
  static getPreferredProvider(): AIProvider {
    return this.config.preferredProvider
  }

  /**
   * Check if any AI service is available
   */
  static isAnyServiceAvailable(): boolean {
    return AIProjectService.isAIServiceAvailable() || CohereProjectService.isCohereServiceAvailable()
  }

  private static getProviderOrder(): AIProvider[] {
    const availableProviders: AIProvider[] = []
    
    // Add available providers
    if (AIProjectService.isAIServiceAvailable()) {
      availableProviders.push('gemini')
    }
    if (CohereProjectService.isCohereServiceAvailable()) {
      availableProviders.push('cohere')
    }

    // If no providers are available, return empty array
    if (availableProviders.length === 0) {
      return []
    }

    // Handle different preferred provider settings
    switch (this.config.preferredProvider) {
      case 'gemini':
        return availableProviders.includes('gemini') 
          ? ['gemini', ...availableProviders.filter(p => p !== 'gemini')]
          : availableProviders

      case 'cohere':
        return availableProviders.includes('cohere')
          ? ['cohere', ...availableProviders.filter(p => p !== 'cohere')]
          : availableProviders

      case 'auto':
      default:
        // Auto mode: prefer the most reliable provider
        // For now, prefer Gemini if available, then Cohere
        if (availableProviders.includes('gemini')) {
          return ['gemini', ...availableProviders.filter(p => p !== 'gemini')]
        }
        return availableProviders
    }
  }

  private static async callProvider(provider: AIProvider, request: AIProjectRequest): Promise<AIProjectResponse> {
    switch (provider) {
      case 'gemini':
        return await AIProjectService.generateProject(request)
      
      case 'cohere':
        return await CohereProjectService.generateProject(request)
      
      default:
        throw new Error(`Unknown AI provider: ${provider}`)
    }
  }

  private static getRecommendedProvider(workingProviders: string[]): string | null {
    if (workingProviders.length === 0) {
      return null
    }

    // Prefer Gemini if available (generally more capable for complex tasks)
    if (workingProviders.includes('gemini')) {
      return 'gemini'
    }

    // Otherwise use Cohere
    if (workingProviders.includes('cohere')) {
      return 'cohere'
    }

    return workingProviders[0] || null
  }

  /**
   * Generate a simple test project to verify AI functionality
   */
  static async generateTestProject(): Promise<{
    success: boolean
    provider: string | null
    response?: AIProjectResponse
    error?: string
  }> {
    try {
      const testRequest: AIProjectRequest = {
        description: 'A simple Hello World React application for testing AI functionality',
        projectType: 'web',
        technologies: ['React', 'TypeScript'],
        features: ['basic-ui'],
        complexity: 'simple',
        includeTests: false,
        includeDocumentation: false
      }

      const response = await this.generateProject(testRequest)
      
      return {
        success: true,
        provider: response.aiProvider,
        response
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return {
        success: false,
        provider: null,
        error: errorMessage
      }
    }
  }
}
