import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileIcon,
  LayoutTemplate,
  Sparkles,
  BarChart3,
  History,
  Menu,
  X,
  HelpCircle,
  Zap
} from 'lucide-react';
import TemplateBrowser from './components/TemplateBrowser';
import AIProjectGenerator from './components/AIProjectGenerator';
import AnalyticsDashboard from './components/AnalyticsDashboard';
import HelpCenter from './components/HelpCenter';
import FileTreePreview from './components/FileTreePreview';
import DownloadSection from './components/DownloadSection';
import OnboardingTour from './components/OnboardingTour';
import ErrorBoundary from './components/ErrorBoundary';
import DebugPanel from './components/DebugPanel';
import { AIProviderTest } from './components/AIProviderTest';
import { ToastProvider, ToastViewport } from './components/ui/Toast';
import { PageLoading } from './components/ui/LoadingStates';
import { useAppStore } from './store/useAppStore';
import { cn } from './lib/utils';

const tabs = [
  { id: 'templates', name: 'Templates', icon: LayoutTemplate },
  { id: 'ai', name: 'AI Generator', icon: Sparkles },
  { id: 'ai-test', name: 'AI Test', icon: Zap },
  { id: 'analytics', name: 'Analytics', icon: BarChart3 },
  { id: 'history', name: 'History', icon: History },
  { id: 'help', name: 'Help', icon: HelpCircle },
];

function MainContent() {
  const {
    currentProject,
    isGenerating,
    activeTab,
    sidebarOpen,
    setSidebarOpen
  } = useAppStore();

  const [showMobileSidebar, setShowMobileSidebar] = useState(false);
  const [isAppLoading, setIsAppLoading] = useState(true);

  // Simulate app initialization
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsAppLoading(false);
    }, 1500);
    return () => clearTimeout(timer);
  }, []);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'templates':
        return <TemplateBrowser />;
      case 'ai':
        return <AIProjectGenerator />;
      case 'ai-test':
        return (
          <div className="p-6">
            <AIProviderTest />
          </div>
        );
      case 'analytics':
        return <AnalyticsDashboard />;
      case 'history':
        return <ProjectHistory />;
      case 'help':
        return <HelpCenter />;
      default:
        return <TemplateBrowser />;
    }
  };

  if (isAppLoading) {
    return <PageLoading />;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Mobile Sidebar Overlay */}
      <AnimatePresence>
        {showMobileSidebar && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 lg:hidden"
          >
            <div
              className="absolute inset-0 bg-black bg-opacity-50"
              onClick={() => setShowMobileSidebar(false)}
            />
            <motion.div
              initial={{ x: -300 }}
              animate={{ x: 0 }}
              exit={{ x: -300 }}
              className="absolute left-0 top-0 h-full w-80 bg-white shadow-xl"
            >
              <Sidebar onClose={() => setShowMobileSidebar(false)} />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Desktop Sidebar */}
      <div className={cn(
        "hidden lg:flex lg:flex-col lg:w-80 bg-white border-r border-gray-200 transition-all duration-300",
        sidebarOpen ? "lg:w-80" : "lg:w-16"
      )}>
        <Sidebar />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => setShowMobileSidebar(true)}
                className="lg:hidden p-2 rounded-md hover:bg-gray-100"
              >
                <Menu className="h-5 w-5" />
              </button>
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="hidden lg:block p-2 rounded-md hover:bg-gray-100"
              >
                <Menu className="h-5 w-5" />
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                {tabs.find(tab => tab.id === activeTab)?.name || 'ProjectForge'}
              </h1>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <div className="flex-1 flex overflow-hidden">
          {/* Main Content */}
          <main className="flex-1 overflow-auto p-6">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              {renderTabContent()}
            </motion.div>
          </main>

          {/* Project Preview Sidebar */}
          {currentProject?.structure && (
            <motion.div
              initial={{ x: 400 }}
              animate={{ x: 0 }}
              className="w-96 bg-white border-l border-gray-200 flex flex-col"
            >
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 mb-2">Project Preview</h2>
                <p className="text-sm text-gray-600">{currentProject.name}</p>
              </div>

              <div className="flex-1 overflow-auto p-6">
                <FileTreePreview structure={currentProject.structure} />
              </div>

              {!isGenerating && (
                <div className="p-6 border-t border-gray-200">
                  <DownloadSection />
                </div>
              )}
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
}

// Sidebar Component
interface SidebarProps {
  onClose?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onClose }) => {
  const { activeTab, setActiveTab, sidebarOpen } = useAppStore();

  return (
    <div className="flex flex-col h-full">
      {/* Logo */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
              <FileIcon className="h-6 w-6 text-white" />
            </div>
            {(sidebarOpen || onClose) && (
              <div>
                <h1 className="text-xl font-bold text-gray-900">ProjectForge</h1>
                <p className="text-sm text-gray-600">AI-Powered Generator</p>
              </div>
            )}
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="p-2 rounded-md hover:bg-gray-100 lg:hidden"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <div className="space-y-2">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;

            return (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id);
                  onClose?.();
                }}
                className={cn(
                  "w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors",
                  isActive
                    ? "bg-blue-50 text-blue-700 border border-blue-200"
                    : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                )}
              >
                <Icon className="h-5 w-5 flex-shrink-0" />
                {(sidebarOpen || onClose) && (
                  <span className="font-medium">{tab.name}</span>
                )}
              </button>
            );
          })}
        </div>
      </nav>

      {/* Footer */}
      {(sidebarOpen || onClose) && (
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 text-center">
            <p>ProjectForge v2.0</p>
            <p>Built with ❤️ and AI</p>
          </div>
        </div>
      )}
    </div>
  );
};

// Project History Component
const ProjectHistory: React.FC = () => {
  const { projectHistory, removeFromHistory, setCurrentProject } = useAppStore();

  if (projectHistory.length === 0) {
    return (
      <div className="text-center py-12">
        <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No project history</h3>
        <p className="text-gray-600">Your generated projects will appear here</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Project History</h2>
        <p className="text-gray-600 mb-6">View and manage your previously generated projects</p>
      </div>

      <div className="grid gap-4">
        {projectHistory.map((item) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm border p-6"
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                  {item.project.name}
                </h3>
                <p className="text-gray-600 mb-2">
                  Created on {new Date(item.createdAt).toLocaleDateString()}
                </p>
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <span>Downloads: {item.downloadCount}</span>
                  {item.project.frontend && (
                    <span>Frontend: {item.project.frontend}</span>
                  )}
                  {item.project.backend && (
                    <span>Backend: {item.project.backend}</span>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setCurrentProject(item.project)}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                >
                  Load Project
                </button>
                <button
                  onClick={() => removeFromHistory(item.id)}
                  className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                >
                  Remove
                </button>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

function App() {
  return (
    <ErrorBoundary>
      <ToastProvider>
        <div className="min-h-screen bg-gray-50">
          <MainContent />
          <OnboardingTour />
          {/* Debug panel - only show in development */}
          {import.meta.env.DEV && <DebugPanel />}
        </div>
        <ToastViewport />
      </ToastProvider>
    </ErrorBoundary>
  );
}

export default App;