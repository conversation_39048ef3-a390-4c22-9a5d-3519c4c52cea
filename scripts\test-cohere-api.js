#!/usr/bin/env node

/**
 * Test script to verify Cohere API connection
 * Run with: node scripts/test-cohere-api.js
 */

import { CohereClient } from 'cohere-ai';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
const envPath = path.join(__dirname, '..', '.env.local');

// Check if .env.local exists
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
} else {
  console.log('⚠️  .env.local file not found, checking process.env...');
}

// Using the provided Cohere API key
const COHERE_API_KEY = '8sYx6Ilex0MDAdrsYHaSWuUi3cdXtmwlB4OJRCQp';

console.log('🔍 Testing Cohere API Connection...\n');

console.log('✅ Using provided Cohere API Key:', COHERE_API_KEY.substring(0, 10) + '...' + COHERE_API_KEY.slice(-4));
console.log('📍 API Key length:', COHERE_API_KEY.length);

// Initialize Cohere client
const cohere = new CohereClient({
  token: COHERE_API_KEY,
});

async function testConnection() {
  try {
    console.log('\n🚀 Initializing Cohere AI...');
    console.log('✅ Client initialized successfully');

    // Test with a simple prompt using the generate endpoint
    console.log('\n📤 Testing Generate endpoint...');
    const prompt = 'Respond with exactly the word "CONNECTED" if you can read this message.';

    const result = await cohere.generate({
      model: 'command-light', // Free tier model
      prompt: prompt,
      maxTokens: 50,
      temperature: 0.1,
    });

    const text = result.generations[0].text.trim();
    console.log('📥 Generate Response received:', text);

    // Check if response is valid
    if (text && text.toUpperCase().includes('CONNECTED')) {
      console.log('\n🎉 SUCCESS: Cohere Generate API is working correctly!');
      return true;
    } else {
      console.log('\n⚠️  WARNING: Unexpected response from Generate API');
      console.log('Expected: "CONNECTED"');
      console.log('Received:', text);
      return false;
    }

  } catch (error) {
    console.error('\n❌ ERROR: Failed to connect to Cohere Generate API');
    console.error('Error details:', error.message);
    console.error('Full error:', error);

    // Provide specific error guidance
    if (error.message.includes('unauthorized') || error.message.includes('401')) {
      console.log('\n💡 Troubleshooting:');
      console.log('   1. Check if your API key is correct');
      console.log('   2. Verify the API key is active in Cohere dashboard');
      console.log('   3. Make sure you have proper permissions');
    } else if (error.message.includes('quota') || error.message.includes('429')) {
      console.log('\n🚨 QUOTA EXCEEDED - FREE TIER LIMITS REACHED');
      console.log('\n💡 Troubleshooting:');
      console.log('   1. You have exceeded your API quota');
      console.log('   2. Check your usage in Cohere dashboard');
      console.log('   3. Wait for quota reset or upgrade your plan');
    } else if (error.message.includes('400')) {
      console.log('\n💡 Troubleshooting:');
      console.log('   1. Bad request - check the request parameters');
      console.log('   2. Model might not be available');
      console.log('   3. Check the prompt format');
    }

    return false;
  }
}

async function testChatEndpoint() {
  try {
    console.log('\n📤 Testing Chat endpoint...');
    
    const chatResponse = await cohere.chat({
      model: 'command-light',
      message: 'Respond with exactly the word "CHAT_CONNECTED" if you can read this message.',
      maxTokens: 50,
    });

    const text = chatResponse.text.trim();
    console.log('📥 Chat Response received:', text);

    if (text && text.toUpperCase().includes('CHAT_CONNECTED')) {
      console.log('✅ Chat endpoint working correctly!');
      return true;
    } else {
      console.log('⚠️  Chat endpoint gave unexpected response');
      console.log('Expected: "CHAT_CONNECTED"');
      console.log('Received:', text);
      return false;
    }

  } catch (error) {
    console.error('❌ Chat endpoint test failed:', error.message);
    return false;
  }
}

async function testProjectGeneration() {
  try {
    console.log('\n🧪 Testing project generation with Cohere...');
    
    const prompt = `Generate a simple JSON response for a React project structure. 
    Respond with only this JSON format:
    {
      "projectName": "test-react-app",
      "structure": {
        "name": "test-react-app",
        "type": "directory",
        "children": [
          {
            "name": "src",
            "type": "directory",
            "children": [
              {
                "name": "App.tsx",
                "type": "file",
                "content": "import React from 'react'\\n\\nfunction App() {\\n  return <div>Hello World</div>\\n}\\n\\nexport default App"
              }
            ]
          }
        ]
      },
      "recommendations": ["Use TypeScript for better development experience"],
      "estimatedTime": "1-2 weeks",
      "difficulty": "Beginner"
    }`;

    const result = await cohere.generate({
      model: 'command-light',
      prompt: prompt,
      maxTokens: 1000,
      temperature: 0.3,
    });

    const text = result.generations[0].text;
    console.log('📥 Project generation response length:', text.length);
    
    // Try to parse JSON
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/) || text.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        console.log('✅ JSON parsing successful');
        console.log('📋 Project name:', parsed.projectName);
        console.log('🏗️  Structure type:', parsed.structure?.type);
        console.log('💡 Recommendations count:', parsed.recommendations?.length || 0);
        return true;
      } else {
        console.log('⚠️  No valid JSON found in response');
        console.log('Raw response:', text.substring(0, 200) + '...');
        return false;
      }
    } catch (parseError) {
      console.log('❌ JSON parsing failed:', parseError.message);
      console.log('Raw response:', text.substring(0, 200) + '...');
      return false;
    }

  } catch (error) {
    console.error('❌ Project generation test failed:', error.message);
    return false;
  }
}

// Run tests
async function runTests() {
  console.log('=' .repeat(60));
  console.log('🧪 COHERE API CONNECTION TEST');
  console.log('=' .repeat(60));

  const connectionTest = await testConnection();
  const chatTest = await testChatEndpoint();
  
  if (connectionTest || chatTest) {
    const generationTest = await testProjectGeneration();
    
    console.log('\n' + '=' .repeat(60));
    console.log('📊 TEST RESULTS');
    console.log('=' .repeat(60));
    console.log('🔗 Generate Test:', connectionTest ? '✅ PASS' : '❌ FAIL');
    console.log('💬 Chat Test:', chatTest ? '✅ PASS' : '❌ FAIL');
    console.log('🏗️  Generation Test:', generationTest ? '✅ PASS' : '❌ FAIL');
    
    if ((connectionTest || chatTest) && generationTest) {
      console.log('\n🎉 COHERE API IS WORKING! You can use it as an alternative to Gemini.');
      console.log('✅ Both basic connection and project generation are functional.');
    } else if (connectionTest || chatTest) {
      console.log('\n✅ Basic connection works, but project generation needs improvement.');
      console.log('💡 You can still use Cohere for simple text generation tasks.');
    } else {
      console.log('\n❌ Connection tests failed. Please check the errors above.');
    }
  } else {
    console.log('\n❌ All connection tests failed. Skipping generation test.');
    console.log('🔧 Please check the API key and try again.');
  }

  console.log('\n📚 Useful links:');
  console.log('   • Cohere Dashboard: https://dashboard.cohere.ai/');
  console.log('   • Cohere API Docs: https://docs.cohere.ai/');
  console.log('   • Cohere Playground: https://dashboard.cohere.ai/playground/');
}

// Run the tests
runTests().catch(console.error);
