// Vercel Serverless Function for Cohere AI Project Generation
import { CohereClient } from 'cohere-ai';

// This runs on the server, so the API key is secure
const cohere = new CohereClient({
  token: process.env.COHERE_API_KEY || '8sYx6Ilex0MDAdrsYHaSWuUi3cdXtmwlB4OJRCQp'
});

export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { prompt, options = {} } = req.body;

    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Check if API key is configured
    const apiKey = process.env.COHERE_API_KEY || '8sYx6Ilex0MDAdrsYHaSWuUi3cdXtmwlB4OJRCQp';
    if (!apiKey) {
      return res.status(500).json({ error: 'Cohere AI service not configured' });
    }

    // Enhanced prompt for better project generation
    const enhancedPrompt = `
Generate a detailed project structure for: ${prompt}

Requirements:
- Include package.json with appropriate dependencies
- Create a logical folder structure
- Add README.md with setup instructions
- Include basic configuration files
- ${options.includeTests ? 'Include test files and testing setup' : 'Skip test files'}
- ${options.includeDocumentation ? 'Include comprehensive documentation' : 'Include basic documentation'}

Format the response as a JSON object with:
{
  "projectName": "kebab-case-name",
  "description": "Brief description",
  "structure": {
    "name": "project-root",
    "type": "directory",
    "children": [...]
  },
  "recommendations": ["tip1", "tip2"],
  "estimatedTime": "X hours",
  "difficulty": "Beginner|Intermediate|Advanced"
}

Ensure all file contents are realistic and functional. Respond with valid JSON only.
`;

    const result = await cohere.generate({
      model: 'command-light', // Free tier model
      prompt: enhancedPrompt,
      maxTokens: 2000,
      temperature: 0.7,
      stopSequences: [],
    });

    const text = result.generations[0]?.text;

    if (!text) {
      return res.status(500).json({ error: 'Empty response from Cohere API' });
    }

    // Try to parse JSON response
    let projectData;
    try {
      // Extract JSON from response (in case there's extra text)
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        projectData = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No JSON found in response');
      }
    } catch (parseError) {
      // If JSON parsing fails, return a structured error
      return res.status(500).json({
        error: 'Failed to parse Cohere AI response',
        rawResponse: text.substring(0, 500) // First 500 chars for debugging
      });
    }

    // Validate required fields
    if (!projectData.projectName || !projectData.structure) {
      return res.status(500).json({
        error: 'Invalid response structure from Cohere AI',
        rawResponse: text.substring(0, 500)
      });
    }

    // Add metadata
    projectData.aiProvider = 'Cohere';
    projectData.generatedAt = new Date().toISOString();

    return res.status(200).json(projectData);

  } catch (error) {
    console.error('Cohere AI Generation Error:', error);
    
    // Return appropriate error response
    if (error.message?.includes('unauthorized') || error.message?.includes('401')) {
      return res.status(401).json({ error: 'Invalid Cohere API key' });
    } else if (error.message?.includes('quota') || error.message?.includes('429')) {
      return res.status(429).json({ error: 'Cohere API quota exceeded' });
    } else if (error.message?.includes('400')) {
      return res.status(400).json({ error: 'Bad request to Cohere API' });
    } else {
      return res.status(500).json({ 
        error: 'Cohere AI service temporarily unavailable',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}
