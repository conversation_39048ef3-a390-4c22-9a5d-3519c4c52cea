#!/usr/bin/env node

/**
 * Comprehensive test script for all AI APIs (Gemini and Cohere)
 * Run with: node scripts/test-all-ai-apis.js
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import { CohereClient } from 'cohere-ai';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
const envPath = path.join(__dirname, '..', '.env.local');

if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
} else {
  console.log('⚠️  .env.local file not found, checking process.env...');
}

// API Keys
const GEMINI_API_KEY = process.env.VITE_GEMINI_API_KEY;
const COHERE_API_KEY = '8sYx6Ilex0MDAdrsYHaSWuUi3cdXtmwlB4OJRCQp';

console.log('🚀 COMPREHENSIVE AI API TESTING SUITE');
console.log('=' .repeat(60));

// Test results storage
const testResults = {
  gemini: {
    available: false,
    connection: false,
    generation: false,
    error: null
  },
  cohere: {
    available: false,
    connection: false,
    generation: false,
    error: null
  }
};

async function testGeminiAPI() {
  console.log('\n🧪 TESTING GEMINI API');
  console.log('-' .repeat(30));

  if (!GEMINI_API_KEY) {
    console.log('❌ Gemini API key not found');
    testResults.gemini.error = 'API key not configured';
    return false;
  }

  console.log('✅ Gemini API key found:', GEMINI_API_KEY.substring(0, 10) + '...');
  testResults.gemini.available = true;

  try {
    const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Test connection
    console.log('📤 Testing connection...');
    const connectionResult = await model.generateContent('Respond with "CONNECTED"');
    const connectionText = await connectionResult.response.text();
    
    if (connectionText.toUpperCase().includes('CONNECTED')) {
      console.log('✅ Connection test passed');
      testResults.gemini.connection = true;
    } else {
      console.log('⚠️ Connection test failed - unexpected response');
      return false;
    }

    // Test project generation
    console.log('📤 Testing project generation...');
    const projectPrompt = `Generate a JSON response for a simple React project:
    {
      "projectName": "test-app",
      "structure": {
        "name": "test-app",
        "type": "directory",
        "children": [
          {
            "name": "src",
            "type": "directory",
            "children": [
              {
                "name": "App.tsx",
                "type": "file",
                "content": "import React from 'react'\\n\\nfunction App() {\\n  return <div>Hello World</div>\\n}\\n\\nexport default App"
              }
            ]
          }
        ]
      },
      "recommendations": ["Use TypeScript"],
      "estimatedTime": "1 week",
      "difficulty": "Beginner"
    }`;

    const projectResult = await model.generateContent(projectPrompt);
    const projectText = await projectResult.response.text();
    
    // Try to parse JSON
    const jsonMatch = projectText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const parsed = JSON.parse(jsonMatch[0]);
      if (parsed.projectName && parsed.structure) {
        console.log('✅ Project generation test passed');
        testResults.gemini.generation = true;
        return true;
      }
    }
    
    console.log('⚠️ Project generation test failed - invalid JSON');
    return false;

  } catch (error) {
    console.log('❌ Gemini API test failed:', error.message);
    testResults.gemini.error = error.message;
    return false;
  }
}

async function testCohereAPI() {
  console.log('\n🧪 TESTING COHERE API');
  console.log('-' .repeat(30));

  console.log('✅ Cohere API key provided:', COHERE_API_KEY.substring(0, 10) + '...');
  testResults.cohere.available = true;

  try {
    const cohere = new CohereClient({
      token: COHERE_API_KEY,
    });

    // Test connection
    console.log('📤 Testing connection...');
    const connectionResult = await cohere.generate({
      model: 'command-light',
      prompt: 'Respond with "CONNECTED"',
      maxTokens: 50,
      temperature: 0.1,
    });
    
    const connectionText = connectionResult.generations[0].text.trim();
    
    if (connectionText.toUpperCase().includes('CONNECTED')) {
      console.log('✅ Connection test passed');
      testResults.cohere.connection = true;
    } else {
      console.log('⚠️ Connection test failed - unexpected response');
      return false;
    }

    // Test project generation
    console.log('📤 Testing project generation...');
    const projectPrompt = `Generate a JSON response for a simple React project:
    {
      "projectName": "test-app",
      "structure": {
        "name": "test-app",
        "type": "directory",
        "children": [
          {
            "name": "src",
            "type": "directory",
            "children": [
              {
                "name": "App.tsx",
                "type": "file",
                "content": "import React from 'react'\\n\\nfunction App() {\\n  return <div>Hello World</div>\\n}\\n\\nexport default App"
              }
            ]
          }
        ]
      },
      "recommendations": ["Use TypeScript"],
      "estimatedTime": "1 week",
      "difficulty": "Beginner"
    }`;

    const projectResult = await cohere.generate({
      model: 'command-light',
      prompt: projectPrompt,
      maxTokens: 1000,
      temperature: 0.3,
    });
    
    const projectText = projectResult.generations[0].text;
    
    // Try to parse JSON
    const jsonMatch = projectText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const parsed = JSON.parse(jsonMatch[0]);
      if (parsed.projectName && parsed.structure) {
        console.log('✅ Project generation test passed');
        testResults.cohere.generation = true;
        return true;
      }
    }
    
    console.log('⚠️ Project generation test failed - invalid JSON');
    return false;

  } catch (error) {
    console.log('❌ Cohere API test failed:', error.message);
    testResults.cohere.error = error.message;
    return false;
  }
}

function generateReport() {
  console.log('\n' + '=' .repeat(60));
  console.log('📊 COMPREHENSIVE TEST RESULTS');
  console.log('=' .repeat(60));

  // Gemini Results
  console.log('\n🤖 GEMINI API:');
  console.log(`   Available: ${testResults.gemini.available ? '✅' : '❌'}`);
  console.log(`   Connection: ${testResults.gemini.connection ? '✅' : '❌'}`);
  console.log(`   Generation: ${testResults.gemini.generation ? '✅' : '❌'}`);
  if (testResults.gemini.error) {
    console.log(`   Error: ${testResults.gemini.error}`);
  }

  // Cohere Results
  console.log('\n🤖 COHERE API:');
  console.log(`   Available: ${testResults.cohere.available ? '✅' : '❌'}`);
  console.log(`   Connection: ${testResults.cohere.connection ? '✅' : '❌'}`);
  console.log(`   Generation: ${testResults.cohere.generation ? '✅' : '❌'}`);
  if (testResults.cohere.error) {
    console.log(`   Error: ${testResults.cohere.error}`);
  }

  // Summary
  const workingAPIs = [];
  if (testResults.gemini.connection && testResults.gemini.generation) {
    workingAPIs.push('Gemini');
  }
  if (testResults.cohere.connection && testResults.cohere.generation) {
    workingAPIs.push('Cohere');
  }

  console.log('\n📋 SUMMARY:');
  console.log(`   Working APIs: ${workingAPIs.length > 0 ? workingAPIs.join(', ') : 'None'}`);
  
  if (workingAPIs.length > 0) {
    console.log('   Recommended: ' + (workingAPIs.includes('Gemini') ? 'Gemini (more capable)' : 'Cohere'));
    console.log('\n🎉 SUCCESS: You have working AI APIs for your project!');
    
    if (workingAPIs.length > 1) {
      console.log('💡 TIP: You can use both APIs with automatic fallback for better reliability.');
    }
  } else {
    console.log('\n❌ No working AI APIs found. Please check your configuration.');
  }

  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  
  if (!testResults.gemini.available) {
    console.log('   • Set up Gemini API key in .env.local: VITE_GEMINI_API_KEY=your_key');
  }
  
  if (testResults.gemini.available && !testResults.gemini.connection) {
    console.log('   • Check Gemini API key validity and quota limits');
  }
  
  if (testResults.cohere.connection && testResults.cohere.generation) {
    console.log('   • Cohere API is working well as a free alternative');
  }
  
  if (workingAPIs.length > 0) {
    console.log('   • Consider implementing multi-AI service for better reliability');
    console.log('   • Test the integration in your React app');
  }

  console.log('\n📚 USEFUL LINKS:');
  console.log('   • Gemini API: https://makersuite.google.com/app/apikey');
  console.log('   • Cohere Dashboard: https://dashboard.cohere.ai/');
  console.log('   • Project Documentation: ./README.md');
}

async function runAllTests() {
  const geminiResult = await testGeminiAPI();
  const cohereResult = await testCohereAPI();
  
  generateReport();
  
  // Exit with appropriate code
  const hasWorkingAPI = geminiResult || cohereResult;
  process.exit(hasWorkingAPI ? 0 : 1);
}

// Run all tests
runAllTests().catch(console.error);
