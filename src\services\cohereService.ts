import { CohereClient } from 'cohere-ai'
import { FileNode } from '../types'
import { logger } from '../utils/logger'
import { AIProjectRequest, AIProjectResponse } from './aiService'

export interface CohereServiceConfig {
  apiKey: string
  model: string
  maxRetries: number
  retryDelay: number
}

export class CohereProjectService {
  private static config = {
    apiKey: '8sYx6Ilex0MDAdrsYHaSWuUi3cdXtmwlB4OJRCQp', // Your provided API key
    model: 'command-light', // Free tier model
    maxRetries: Number(import.meta.env.VITE_AI_MAX_RETRIES) || 3,
    retryDelay: Number(import.meta.env.VITE_AI_RETRY_DELAY) || 1000,
    timeout: Number(import.meta.env.VITE_AI_TIMEOUT) || 30000,
    enabled: import.meta.env.VITE_COHERE_SERVICE_ENABLED !== 'false',
  }

  private static cohere: CohereClient | null = null

  private static initializeCohere(): void {
    console.log('🔧 Initializing Cohere with config:', {
      hasApiKey: !!this.config.apiKey,
      apiKeyLength: this.config.apiKey?.length || 0,
      model: this.config.model
    })

    if (!this.config.apiKey) {
      throw new Error('Cohere API key not configured.')
    }

    if (!this.cohere) {
      this.cohere = new CohereClient({
        token: this.config.apiKey,
      })
      console.log('✅ Cohere AI initialized successfully')
      logger.info('Cohere AI initialized', { model: this.config.model }, 'cohere-service')
    }
  }

  static async generateProject(request: AIProjectRequest): Promise<AIProjectResponse> {
    try {
      if (!this.config.enabled) {
        console.log('🔄 Cohere service disabled, using fallback generation...')
        return this.generateFallbackProject(request)
      }

      logger.info('Starting Cohere project generation', {
        projectType: request.projectType,
        technologies: request.technologies,
        complexity: request.complexity
      }, 'cohere-service')

      console.log('🚀 Calling Cohere API...')
      const response = await this.callCohereAPI(request)

      logger.info('Cohere project generation completed', {
        projectName: response.projectName,
        difficulty: response.difficulty
      }, 'cohere-service')

      console.log('✅ Cohere generation successful!')
      return response
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.error('❌ Cohere project generation failed:', error)
      logger.error('Cohere project generation failed', error, 'cohere-service')

      // Check for specific API errors
      if (errorMessage?.includes('429') || errorMessage?.includes('quota')) {
        throw new Error('Cohere service quota exceeded. Please check your API usage limits.')
      }

      if (errorMessage?.includes('401') || errorMessage?.includes('unauthorized')) {
        throw new Error('Cohere service configuration error. Please contact support.')
      }

      if (errorMessage?.includes('400')) {
        throw new Error('Invalid request to Cohere service. Please try again.')
      }

      // Fallback to basic generation if Cohere fails
      console.log('🔄 Falling back to basic project generation...')
      logger.info('Falling back to basic project generation', undefined, 'cohere-service')
      return this.generateFallbackProject(request)
    }
  }

  private static async callCohereAPI(request: AIProjectRequest): Promise<AIProjectResponse> {
    this.initializeCohere()
    
    const prompt = this.buildPrompt(request)

    const response = await this.callCohereWithRetry(prompt)
    return this.parseAIResponse(response, request)
  }

  private static buildPrompt(request: AIProjectRequest): string {
    return `You are an expert software architect. Generate a detailed project structure based on the following requirements:

Project Description: ${request.description}
Project Type: ${request.projectType}
Technologies: ${request.technologies.join(', ')}
Features: ${request.features.join(', ')}
Complexity: ${request.complexity}
Include Tests: ${request.includeTests}
Include Documentation: ${request.includeDocumentation}

Please respond with a JSON object containing:
1. projectName: A kebab-case project name based on the description
2. structure: A nested file/folder structure with realistic file names and basic content
3. recommendations: Array of 3-5 specific recommendations for this project
4. estimatedTime: Development time estimate (e.g., "2-3 weeks")
5. difficulty: "Beginner", "Intermediate", or "Advanced"

The structure should be a nested object where:
- Each folder has: { name: "folder-name", type: "directory", children: [...] }
- Each file has: { name: "file-name.ext", type: "file", content: "basic file content" }

Make the structure realistic and follow modern best practices for the chosen technologies.

Example structure format:
{
  "projectName": "my-awesome-app",
  "structure": {
    "name": "my-awesome-app",
    "type": "directory",
    "children": [
      {
        "name": "src",
        "type": "directory",
        "children": [
          {
            "name": "App.tsx",
            "type": "file",
            "content": "import React from 'react'\\n\\nfunction App() {\\n  return <div>Hello World</div>\\n}\\n\\nexport default App"
          }
        ]
      },
      {
        "name": "package.json",
        "type": "file",
        "content": "{\\n  \\"name\\": \\"my-awesome-app\\",\\n  \\"version\\": \\"1.0.0\\"\\n}"
      }
    ]
  },
  "recommendations": ["Use TypeScript for better type safety", "Add ESLint for code quality"],
  "estimatedTime": "2-3 weeks",
  "difficulty": "Intermediate"
}`
  }

  private static async callCohereWithRetry(prompt: string): Promise<string> {
    if (!this.cohere) {
      throw new Error('Cohere AI not initialized')
    }

    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        logger.debug(`Cohere API call attempt ${attempt}`, { model: this.config.model }, 'cohere-service')

        const result = await this.cohere.generate({
          model: this.config.model,
          prompt: prompt,
          maxTokens: 2000,
          temperature: 0.7,
          stopSequences: [],
        })

        const text = result.generations[0]?.text

        if (!text) {
          throw new Error('Empty response from Cohere API')
        }

        logger.debug('Cohere API call successful', {
          responseLength: text.length,
          attempt
        }, 'cohere-service')

        return text
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        logger.warn(`Cohere API call failed (attempt ${attempt})`, {
          error: errorMessage,
          attempt,
          maxRetries: this.config.maxRetries
        }, 'cohere-service')

        if (attempt === this.config.maxRetries) {
          throw new Error(`Cohere API failed after ${this.config.maxRetries} attempts: ${errorMessage}`)
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * attempt))
      }
    }

    throw new Error('Unexpected error in Cohere API retry logic')
  }

  private static parseAIResponse(aiResponse: string, request: AIProjectRequest): AIProjectResponse {
    try {
      // Extract JSON from the response (handle markdown code blocks)
      const jsonMatch = aiResponse.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/) ||
                       aiResponse.match(/(\{[\s\S]*\})/)

      if (!jsonMatch) {
        throw new Error('No JSON found in AI response')
      }

      const parsed = JSON.parse(jsonMatch[1])

      // Validate required fields
      if (!parsed.projectName || !parsed.structure || !parsed.recommendations) {
        throw new Error('Invalid AI response structure')
      }

      return {
        projectName: this.sanitizeProjectName(parsed.projectName),
        description: request.description,
        structure: this.validateAndCleanStructure(parsed.structure),
        recommendations: Array.isArray(parsed.recommendations) ? parsed.recommendations.slice(0, 5) : [],
        estimatedTime: parsed.estimatedTime || this.estimateTime(request.complexity),
        difficulty: this.validateDifficulty(parsed.difficulty) || this.determineDifficulty(request),
        aiProvider: 'Cohere'
      }
    } catch (error) {
      logger.error('Failed to parse Cohere response', { error, response: aiResponse.substring(0, 500) }, 'cohere-service')
      throw new Error('Failed to parse Cohere response')
    }
  }

  private static sanitizeProjectName(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9-_\s]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 50) || 'cohere-generated-project'
  }

  private static validateDifficulty(difficulty: string): 'Beginner' | 'Intermediate' | 'Advanced' | null {
    const validDifficulties = ['Beginner', 'Intermediate', 'Advanced']
    return validDifficulties.includes(difficulty) ? difficulty as 'Beginner' | 'Intermediate' | 'Advanced' : null
  }

  private static validateAndCleanStructure(structure: unknown): FileNode {
    if (!structure || typeof structure !== 'object') {
      throw new Error('Invalid structure format')
    }

    const cleanNode = (node: Record<string, unknown>): FileNode => {
      if (!node.name || !node.type) {
        throw new Error('Node missing required fields')
      }

      const cleanedNode: FileNode = {
        name: String(node.name).substring(0, 100), // Limit name length
        type: node.type === 'directory' ? 'directory' : 'file'
      }

      if (node.type === 'directory' && Array.isArray(node.children)) {
        cleanedNode.children = node.children.map(cleanNode).slice(0, 50) // Limit children
      } else if (node.type === 'file' && node.content) {
        cleanedNode.content = String(node.content).substring(0, 5000) // Limit content length
      }

      return cleanedNode
    }

    return cleanNode(structure)
  }

  // Reuse fallback methods from the main AI service
  private static generateFallbackProject(request: AIProjectRequest): AIProjectResponse {
    // Import and use the fallback logic from aiService
    // For now, return a simple fallback
    return {
      projectName: 'cohere-fallback-project',
      description: request.description,
      structure: {
        name: 'cohere-fallback-project',
        type: 'directory',
        children: [
          {
            name: 'README.md',
            type: 'file',
            content: '# Cohere Fallback Project\n\nGenerated when Cohere API was unavailable.'
          }
        ]
      },
      recommendations: ['Set up proper AI service configuration'],
      estimatedTime: '1-2 weeks',
      difficulty: 'Beginner',
      aiProvider: 'Cohere Fallback'
    }
  }

  private static estimateTime(complexity: 'simple' | 'medium' | 'complex'): string {
    switch (complexity) {
      case 'simple':
        return '1-2 weeks'
      case 'medium':
        return '3-6 weeks'
      case 'complex':
        return '2-4 months'
      default:
        return '2-4 weeks'
    }
  }

  private static determineDifficulty(request: AIProjectRequest): 'Beginner' | 'Intermediate' | 'Advanced' {
    const { complexity, technologies, features } = request

    if (complexity === 'simple' && technologies.length <= 2 && features.length <= 3) {
      return 'Beginner'
    }

    if (complexity === 'complex' || technologies.length > 4 || features.length > 6) {
      return 'Advanced'
    }

    return 'Intermediate'
  }

  // Public method to check if Cohere service is available
  static isCohereServiceAvailable(): boolean {
    return !!(this.config.apiKey && this.config.enabled)
  }

  // Public method to get Cohere service info
  static getCohereServiceInfo(): { provider: string; model: string; available: boolean } {
    return {
      provider: 'Cohere',
      model: this.config.model,
      available: this.isCohereServiceAvailable()
    }
  }

  // Public method to test Cohere connection
  static async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🧪 Testing Cohere connection...')

      if (!this.isCohereServiceAvailable()) {
        return { success: false, message: 'Cohere service disabled in configuration' }
      }

      console.log('🔑 API key available, initializing Cohere...')
      this.initializeCohere()

      console.log('📤 Sending test prompt...')
      const testPrompt = 'Respond with just the word "connected" if you can read this message.'
      const response = await this.callCohereWithRetry(testPrompt)

      console.log('📥 Received response:', response)

      if (response.toLowerCase().includes('connected')) {
        return { success: true, message: 'Cohere service connected successfully' }
      } else {
        return { success: false, message: 'Unexpected response from Cohere service' }
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Connection test failed'
      console.error('❌ Cohere connection test failed:', error)
      logger.error('Cohere connection test failed', error, 'cohere-service')
      return { success: false, message: errorMessage }
    }
  }
}
