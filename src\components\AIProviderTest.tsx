import React, { useState } from 'react'
import { <PERSON><PERSON> } from './ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/Card'
import { Badge } from './ui/Badge'
import { Loader2, CheckCircle, XCircle, Zap } from 'lucide-react'

interface TestResult {
  success: boolean
  message: string
  provider?: string
  response?: any
  error?: string
}

interface ProviderStatus {
  gemini: TestResult | null
  cohere: TestResult | null
  loading: boolean
}

export const AIProviderTest: React.FC = () => {
  const [status, setStatus] = useState<ProviderStatus>({
    gemini: null,
    cohere: null,
    loading: false
  })

  const testGeminiAPI = async (): Promise<TestResult> => {
    try {
      const response = await fetch('/api/generate-project', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: 'Create a simple React TypeScript component that displays "Hello World"',
          options: {
            includeTests: false,
            includeDocumentation: false,
          }
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const data = await response.json()
      return {
        success: true,
        message: 'Gemini API working correctly',
        provider: 'Gemini',
        response: data
      }
    } catch (error) {
      return {
        success: false,
        message: 'Gemini API failed',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  const testCohereAPI = async (): Promise<TestResult> => {
    try {
      const response = await fetch('/api/generate-project-cohere', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: 'Create a simple React TypeScript component that displays "Hello World"',
          options: {
            includeTests: false,
            includeDocumentation: false,
          }
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const data = await response.json()
      return {
        success: true,
        message: 'Cohere API working correctly',
        provider: 'Cohere',
        response: data
      }
    } catch (error) {
      return {
        success: false,
        message: 'Cohere API failed',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  const runAllTests = async () => {
    setStatus(prev => ({ ...prev, loading: true }))

    // Test Gemini
    const geminiResult = await testGeminiAPI()
    setStatus(prev => ({ ...prev, gemini: geminiResult }))

    // Test Cohere
    const cohereResult = await testCohereAPI()
    setStatus(prev => ({ ...prev, cohere: cohereResult, loading: false }))
  }

  const renderTestResult = (result: TestResult | null, providerName: string) => {
    if (!result) {
      return (
        <div className="flex items-center gap-2 text-gray-500">
          <div className="w-4 h-4 rounded-full border-2 border-gray-300" />
          <span>Not tested</span>
        </div>
      )
    }

    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          {result.success ? (
            <CheckCircle className="w-4 h-4 text-green-500" />
          ) : (
            <XCircle className="w-4 h-4 text-red-500" />
          )}
          <span className={result.success ? 'text-green-700' : 'text-red-700'}>
            {result.message}
          </span>
        </div>
        
        {result.success && result.response && (
          <div className="text-sm text-gray-600">
            <p>✅ Project: {result.response.projectName}</p>
            <p>✅ Provider: {result.response.aiProvider}</p>
            <p>✅ Generated at: {new Date(result.response.generatedAt).toLocaleTimeString()}</p>
          </div>
        )}
        
        {!result.success && result.error && (
          <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
            <strong>Error:</strong> {result.error}
          </div>
        )}
      </div>
    )
  }

  const getWorkingProviders = () => {
    const working = []
    if (status.gemini?.success) working.push('Gemini')
    if (status.cohere?.success) working.push('Cohere')
    return working
  }

  const workingProviders = getWorkingProviders()

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="w-5 h-5" />
          AI Provider Testing
        </CardTitle>
        <CardDescription>
          Test the connection and functionality of available AI providers
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <Button 
          onClick={runAllTests} 
          disabled={status.loading}
          className="w-full"
        >
          {status.loading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Testing AI Providers...
            </>
          ) : (
            'Test All AI Providers'
          )}
        </Button>

        <div className="grid gap-4">
          <div className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold">Google Gemini</h3>
              <Badge variant={status.gemini?.success ? 'default' : status.gemini ? 'destructive' : 'secondary'}>
                {status.gemini?.success ? 'Working' : status.gemini ? 'Failed' : 'Untested'}
              </Badge>
            </div>
            {renderTestResult(status.gemini, 'Gemini')}
          </div>

          <div className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold">Cohere</h3>
              <Badge variant={status.cohere?.success ? 'default' : status.cohere ? 'destructive' : 'secondary'}>
                {status.cohere?.success ? 'Working' : status.cohere ? 'Failed' : 'Untested'}
              </Badge>
            </div>
            {renderTestResult(status.cohere, 'Cohere')}
          </div>
        </div>

        {(status.gemini || status.cohere) && (
          <div className="border-t pt-4">
            <h3 className="font-semibold mb-2">Summary</h3>
            {workingProviders.length > 0 ? (
              <div className="text-green-700 bg-green-50 p-3 rounded">
                <p className="font-medium">✅ {workingProviders.length} AI provider(s) working!</p>
                <p className="text-sm">Working providers: {workingProviders.join(', ')}</p>
                {workingProviders.length > 1 && (
                  <p className="text-sm mt-1">💡 You can use automatic fallback between providers for better reliability.</p>
                )}
              </div>
            ) : (
              <div className="text-red-700 bg-red-50 p-3 rounded">
                <p className="font-medium">❌ No AI providers are currently working</p>
                <p className="text-sm">Please check your API keys and try again.</p>
              </div>
            )}
          </div>
        )}

        <div className="text-xs text-gray-500 space-y-1">
          <p>💡 <strong>Tip:</strong> Cohere offers a generous free tier that's perfect for testing</p>
          <p>🔧 <strong>Note:</strong> Gemini may be temporarily overloaded during peak usage</p>
          <p>📚 <strong>Docs:</strong> Check the README.md for setup instructions</p>
        </div>
      </CardContent>
    </Card>
  )
}
