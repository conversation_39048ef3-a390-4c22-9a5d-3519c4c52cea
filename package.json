{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "tsc && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "test:ai": "node scripts/test-gemini-api.js", "test:cohere": "node scripts/test-cohere-api.js", "test:ai:all": "node scripts/test-all-ai-apis.js", "deploy": "npm run build && vercel --prod", "deploy:preview": "npm run build && vercel"}, "dependencies": {"@google/generative-ai": "^0.2.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cohere-ai": "^7.17.1", "framer-motion": "^11.0.0", "jszip": "^3.10.1", "lucide-react": "^0.344.0", "postcss": "^8.4.35", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.49.0", "react-router-dom": "^6.21.0", "recharts": "^2.10.0", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "vite": "^6.3.4", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.11", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "dotenv": "^16.6.1", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "^29.1.1", "typescript-eslint": "^8.3.0", "vitest": "^1.2.0"}}