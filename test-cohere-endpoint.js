// Simple test for Cohere API endpoint
import { CohereClient } from 'cohere-ai';

const cohere = new CohereClient({
  token: '8sYx6Ilex0MDAdrsYHaSWuUi3cdXtmwlB4OJRCQp'
});

async function testCohereEndpoint() {
  try {
    console.log('🧪 Testing Cohere API endpoint...');
    
    const result = await cohere.generate({
      model: 'command-light',
      prompt: 'Generate a simple JSON object with a "message" field containing "Hello from Cohere"',
      maxTokens: 100,
      temperature: 0.1,
    });

    const response = result.generations[0].text;
    console.log('✅ Cohere API Response:');
    console.log(response);
    
    // Try to extract JSON
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      try {
        const parsed = JSON.parse(jsonMatch[0]);
        console.log('✅ Parsed JSON:', parsed);
      } catch (e) {
        console.log('⚠️ Could not parse JSON, but got response');
      }
    }
    
    console.log('🎉 Cohere API is working correctly!');
    
  } catch (error) {
    console.error('❌ Cohere API test failed:', error.message);
  }
}

testCohereEndpoint();
